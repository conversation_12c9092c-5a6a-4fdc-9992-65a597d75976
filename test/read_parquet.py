#!/usr/bin/env python3
"""
读取 parquet 文件的脚本
"""

import pandas as pd
import os
import sys
import json

def read_parquet_file(file_path):
    """
    读取 parquet 文件并显示基本信息
    
    Args:
        file_path (str): parquet 文件路径
    """
    try:
        # 检查文件是否存在
        if not os.path.exists(file_path):
            print(f"错误：文件 {file_path} 不存在")
            return None
        
        print(f"正在读取文件：{file_path}")
        print("-" * 50)
        
        # 读取 parquet 文件
        df = pd.read_parquet(file_path)
        
        # 显示基本信息
        print(f"数据形状：{df.shape}")
        print(f"列数：{len(df.columns)}")
        print(f"行数：{len(df)}")
        print()
        
        # 显示列名和数据类型
        print("列信息：")
        for i, (col, dtype) in enumerate(df.dtypes.items()):
            print(f"  {i+1}. {col}: {dtype}")
        print()
        
        # 显示前几行数据
        print("前5行数据：")
        print(df.head())
        print()
        
        # 显示数据统计信息（仅数值列）
        numeric_cols = df.select_dtypes(include=['number']).columns
        if len(numeric_cols) > 0:
            print("数值列统计信息：")
            print(df[numeric_cols].describe())
            print()
        
        # 显示缺失值信息
        missing_values = df.isnull().sum()
        if missing_values.sum() > 0:
            print("缺失值统计：")
            for col, missing in missing_values.items():
                if missing > 0:
                    print(f"  {col}: {missing} ({missing/len(df)*100:.2f}%)")
        else:
            print("没有缺失值")
        
        return df
        
    except Exception as e:
        print(f"读取文件时出错：{e}")
        return None

def count_test_cases(df):
    """
    统计每道题目的测试点数量

    Args:
        df (pd.DataFrame): 包含题目数据的DataFrame
    """
    print("=" * 60)
    print("每道题目的测试点数量统计")
    print("=" * 60)

    test_case_counts = []

    for idx, row in df.iterrows():
        slug_name = row['slug_name']
        test_cases = row['test_cases']

        # 计算测试用例数量
        count = 0
        try:
            if isinstance(test_cases, str):
                # 解析 JSON 字符串
                test_cases_list = json.loads(test_cases)
                if isinstance(test_cases_list, list):
                    count = len(test_cases_list)
            elif isinstance(test_cases, list):
                count = len(test_cases)
        except (json.JSONDecodeError, TypeError) as e:
            print(f"警告: 解析 {slug_name} 的测试用例时出错: {e}")
            count = 0

        test_case_counts.append({
            'slug_name': slug_name,
            'test_count': count,
            'difficulty': row.get('difficulty', 'Unknown')
        })

        print(f"{idx+1:3d}. {slug_name:<30} | 测试点: {count:3d} | 难度: {row.get('difficulty', 'Unknown')}")

    # 统计信息
    counts = [item['test_count'] for item in test_case_counts]
    print("\n" + "=" * 60)
    print("统计摘要:")
    print(f"总题目数: {len(counts)}")
    if counts:
        print(f"平均测试点数: {sum(counts)/len(counts):.2f}")
        print(f"最少测试点数: {min(counts)}")
        print(f"最多测试点数: {max(counts)}")

    # 按难度分组统计
    difficulty_stats = {}
    for item in test_case_counts:
        diff = item['difficulty']
        if diff not in difficulty_stats:
            difficulty_stats[diff] = []
        difficulty_stats[diff].append(item['test_count'])

    print("\n按难度分组统计:")
    for difficulty, counts in difficulty_stats.items():
        if counts:
            avg_count = sum(counts) / len(counts)
            print(f"  {difficulty}: 平均 {avg_count:.2f} 个测试点 (共 {len(counts)} 题)")

    return test_case_counts

def main():
    """主函数"""
    # 获取当前目录下的 parquet 文件
    current_dir = os.path.dirname(os.path.abspath(__file__))
    parquet_files = [f for f in os.listdir(current_dir) if f.endswith('.parquet')]

    if not parquet_files:
        print("当前目录下没有找到 parquet 文件")
        return

    # 如果有命令行参数，使用指定的文件
    if len(sys.argv) > 1:
        file_path = sys.argv[1]
        if not file_path.endswith('.parquet'):
            print("请指定一个 .parquet 文件")
            return
    else:
        # 使用找到的第一个 parquet 文件
        file_path = os.path.join(current_dir, parquet_files[0])
        print(f"找到 parquet 文件：{parquet_files}")
        print(f"将读取：{parquet_files[0]}")
        print()

    # 读取文件
    df = read_parquet_file(file_path)

    if df is not None:
        # 统计测试点数量
        test_case_counts = count_test_cases(df)

        # 询问是否要保存统计结果
        while True:
            choice = input("\n是否要保存测试点统计结果？(csv/excel/json/n): ").lower().strip()
            if choice == 'n':
                break
            elif choice in ['csv', 'excel', 'json']:
                import pandas as pd
                stats_df = pd.DataFrame(test_case_counts)
                base_name = os.path.splitext(os.path.basename(file_path))[0]

                if choice == 'csv':
                    output_file = f"{base_name}_test_counts.csv"
                    stats_df.to_csv(output_file, index=False)
                    print(f"测试点统计已保存为：{output_file}")
                elif choice == 'excel':
                    output_file = f"{base_name}_test_counts.xlsx"
                    stats_df.to_excel(output_file, index=False)
                    print(f"测试点统计已保存为：{output_file}")
                elif choice == 'json':
                    output_file = f"{base_name}_test_counts.json"
                    stats_df.to_json(output_file, orient='records', indent=2)
                    print(f"测试点统计已保存为：{output_file}")
                break
            else:
                print("请输入 csv、excel、json 或 n")

if __name__ == "__main__":
    main()
