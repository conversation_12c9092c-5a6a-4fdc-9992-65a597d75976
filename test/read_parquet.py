#!/usr/bin/env python3
"""
读取 parquet 文件的脚本
"""

import pandas as pd
import os
import sys

def read_parquet_file(file_path):
    """
    读取 parquet 文件并显示基本信息
    
    Args:
        file_path (str): parquet 文件路径
    """
    try:
        # 检查文件是否存在
        if not os.path.exists(file_path):
            print(f"错误：文件 {file_path} 不存在")
            return None
        
        print(f"正在读取文件：{file_path}")
        print("-" * 50)
        
        # 读取 parquet 文件
        df = pd.read_parquet(file_path)
        
        # 显示基本信息
        print(f"数据形状：{df.shape}")
        print(f"列数：{len(df.columns)}")
        print(f"行数：{len(df)}")
        print()
        
        # 显示列名和数据类型
        print("列信息：")
        for i, (col, dtype) in enumerate(df.dtypes.items()):
            print(f"  {i+1}. {col}: {dtype}")
        print()
        
        # 显示前几行数据
        print("前5行数据：")
        print(df.head())
        print()
        
        # 显示数据统计信息（仅数值列）
        numeric_cols = df.select_dtypes(include=['number']).columns
        if len(numeric_cols) > 0:
            print("数值列统计信息：")
            print(df[numeric_cols].describe())
            print()
        
        # 显示缺失值信息
        missing_values = df.isnull().sum()
        if missing_values.sum() > 0:
            print("缺失值统计：")
            for col, missing in missing_values.items():
                if missing > 0:
                    print(f"  {col}: {missing} ({missing/len(df)*100:.2f}%)")
        else:
            print("没有缺失值")
        
        return df
        
    except Exception as e:
        print(f"读取文件时出错：{e}")
        return None

def main():
    """主函数"""
    # 获取当前目录下的 parquet 文件
    current_dir = os.path.dirname(os.path.abspath(__file__))
    parquet_files = [f for f in os.listdir(current_dir) if f.endswith('.parquet')]
    
    if not parquet_files:
        print("当前目录下没有找到 parquet 文件")
        return
    
    # 如果有命令行参数，使用指定的文件
    if len(sys.argv) > 1:
        file_path = sys.argv[1]
        if not file_path.endswith('.parquet'):
            print("请指定一个 .parquet 文件")
            return
    else:
        # 使用找到的第一个 parquet 文件
        file_path = os.path.join(current_dir, parquet_files[0])
        print(f"找到 parquet 文件：{parquet_files}")
        print(f"将读取：{parquet_files[0]}")
        print()
    
    # 读取文件
    df = read_parquet_file(file_path)
    
    if df is not None:
        print("\n读取完成！")
        
        # 询问是否要保存为其他格式
        while True:
            choice = input("\n是否要将数据保存为其他格式？(csv/excel/json/n): ").lower().strip()
            if choice == 'n':
                break
            elif choice in ['csv', 'excel', 'json']:
                base_name = os.path.splitext(os.path.basename(file_path))[0]
                if choice == 'csv':
                    output_file = f"{base_name}.csv"
                    df.to_csv(output_file, index=False)
                    print(f"已保存为：{output_file}")
                elif choice == 'excel':
                    output_file = f"{base_name}.xlsx"
                    df.to_excel(output_file, index=False)
                    print(f"已保存为：{output_file}")
                elif choice == 'json':
                    output_file = f"{base_name}.json"
                    df.to_json(output_file, orient='records', indent=2)
                    print(f"已保存为：{output_file}")
                break
            else:
                print("请输入 csv、excel、json 或 n")

if __name__ == "__main__":
    main()
