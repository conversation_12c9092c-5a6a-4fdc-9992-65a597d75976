#!/usr/bin/env python3
"""
快速显示测试点数量统计摘要
"""

import pandas as pd
import json
import sys

def get_test_count_summary(file_path):
    """获取测试点数量统计摘要"""
    df = pd.read_parquet(file_path)
    
    print("=" * 60)
    print("测试点数量统计摘要")
    print("=" * 60)
    
    test_counts = []
    difficulty_stats = {}
    
    for idx, row in df.iterrows():
        slug_name = row['slug_name']
        test_cases = row['test_cases']
        difficulty = row.get('difficulty', 'Unknown')
        
        # 计算测试用例数量
        count = 0
        try:
            if isinstance(test_cases, str):
                test_cases_list = json.loads(test_cases)
                if isinstance(test_cases_list, list):
                    count = len(test_cases_list)
        except (json.JSONDecodeError, TypeError):
            count = 0
        
        test_counts.append(count)
        
        # 按难度分组
        if difficulty not in difficulty_stats:
            difficulty_stats[difficulty] = []
        difficulty_stats[difficulty].append(count)
    
    # 总体统计
    print(f"总题目数: {len(test_counts)}")
    print(f"平均测试点数: {sum(test_counts)/len(test_counts):.2f}")
    print(f"最少测试点数: {min(test_counts)}")
    print(f"最多测试点数: {max(test_counts)}")
    
    # 按难度统计
    print(f"\n按难度分组统计:")
    for difficulty, counts in difficulty_stats.items():
        avg_count = sum(counts) / len(counts)
        print(f"  {difficulty}: 平均 {avg_count:.2f} 个测试点 (共 {len(counts)} 题)")
    
    # 测试点数量分布
    print(f"\n测试点数量分布:")
    count_distribution = {}
    for count in test_counts:
        count_distribution[count] = count_distribution.get(count, 0) + 1
    
    for count in sorted(count_distribution.keys()):
        print(f"  {count} 个测试点: {count_distribution[count]} 题")
    
    return test_counts, difficulty_stats

def main():
    if len(sys.argv) > 1:
        file_path = sys.argv[1]
    else:
        file_path = "eval-00000-of-00001.parquet"
    
    print(f"分析文件: {file_path}")
    get_test_count_summary(file_path)

if __name__ == "__main__":
    main()
