#!/usr/bin/env python3
"""
快速读取和预览 parquet 文件
"""

import pandas as pd
import sys
import os

def quick_read(file_path):
    """快速读取并显示 parquet 文件的基本信息"""
    try:
        df = pd.read_parquet(file_path)
        
        print(f"文件：{file_path}")
        print(f"形状：{df.shape} (行数 x 列数)")
        print(f"列名：{list(df.columns)}")
        print("\n前3行数据：")
        print(df.head(3))
        
        return df
    except Exception as e:
        print(f"错误：{e}")
        return None

if __name__ == "__main__":
    if len(sys.argv) > 1:
        file_path = sys.argv[1]
    else:
        # 查找当前目录的 parquet 文件
        parquet_files = [f for f in os.listdir('.') if f.endswith('.parquet')]
        if parquet_files:
            file_path = parquet_files[0]
            print(f"使用文件：{file_path}\n")
        else:
            print("请指定 parquet 文件路径，或确保当前目录有 .parquet 文件")
            sys.exit(1)
    
    quick_read(file_path)
