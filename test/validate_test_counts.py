#!/usr/bin/env python3
"""
交叉验证测试点数量统计
"""

import pandas as pd
import json
import sys

def validate_test_counts(file_path):
    """
    交叉验证测试点数量统计
    """
    print("=" * 80)
    print("交叉验证测试点数量统计")
    print("=" * 80)
    
    df = pd.read_parquet(file_path)
    
    validation_results = []
    discrepancies = []
    
    for idx, row in df.iterrows():
        slug_name = row['slug_name']
        test_cases = row['test_cases']
        difficulty = row.get('difficulty', 'Unknown')
        
        # 方法1: 统计 JSON 数组长度
        json_array_count = 0
        # 方法2: 统计 "input" 字段数量
        input_field_count = 0
        # 方法3: 统计 "expected" 字段数量
        expected_field_count = 0
        
        try:
            if isinstance(test_cases, str):
                # 解析 JSON 字符串
                test_cases_list = json.loads(test_cases)
                if isinstance(test_cases_list, list):
                    json_array_count = len(test_cases_list)
                    
                    # 统计 input 和 expected 字段
                    for test_case in test_cases_list:
                        if isinstance(test_case, dict):
                            if "input" in test_case:
                                input_field_count += 1
                            if "expected" in test_case:
                                expected_field_count += 1
                                
        except (json.JSONDecodeError, TypeError) as e:
            print(f"错误: 解析 {slug_name} 时出错: {e}")
            continue
        
        # 检查是否有差异
        if not (json_array_count == input_field_count == expected_field_count):
            discrepancies.append({
                'slug_name': slug_name,
                'json_array': json_array_count,
                'input_fields': input_field_count,
                'expected_fields': expected_field_count
            })
        
        validation_results.append({
            'slug_name': slug_name,
            'difficulty': difficulty,
            'json_array_count': json_array_count,
            'input_field_count': input_field_count,
            'expected_field_count': expected_field_count,
            'consistent': json_array_count == input_field_count == expected_field_count
        })
        
        # 显示结果
        status = "✓" if json_array_count == input_field_count == expected_field_count else "✗"
        print(f"{idx+1:3d}. {slug_name:<35} | 数组:{json_array_count:3d} | input:{input_field_count:3d} | expected:{expected_field_count:3d} | {status}")
    
    # 统计摘要
    print("\n" + "=" * 80)
    print("验证摘要:")
    
    total_problems = len(validation_results)
    consistent_problems = sum(1 for r in validation_results if r['consistent'])
    
    print(f"总题目数: {total_problems}")
    print(f"数据一致的题目: {consistent_problems}")
    print(f"数据不一致的题目: {total_problems - consistent_problems}")
    
    if discrepancies:
        print(f"\n发现 {len(discrepancies)} 个数据不一致的题目:")
        for disc in discrepancies:
            print(f"  {disc['slug_name']}: 数组={disc['json_array']}, input={disc['input_fields']}, expected={disc['expected_fields']}")
    else:
        print("\n✓ 所有题目的数据都是一致的!")
    
    # 统计信息
    json_counts = [r['json_array_count'] for r in validation_results]
    input_counts = [r['input_field_count'] for r in validation_results]
    
    if json_counts:
        print(f"\n基于 JSON 数组长度的统计:")
        print(f"  平均测试点数: {sum(json_counts)/len(json_counts):.2f}")
        print(f"  最少测试点数: {min(json_counts)}")
        print(f"  最多测试点数: {max(json_counts)}")
        
        print(f"\n基于 input 字段数量的统计:")
        print(f"  平均测试点数: {sum(input_counts)/len(input_counts):.2f}")
        print(f"  最少测试点数: {min(input_counts)}")
        print(f"  最多测试点数: {max(input_counts)}")
    
    # 按难度分组统计
    difficulty_stats = {}
    for result in validation_results:
        diff = result['difficulty']
        if diff not in difficulty_stats:
            difficulty_stats[diff] = []
        difficulty_stats[diff].append(result['json_array_count'])
    
    print(f"\n按难度分组统计 (基于 JSON 数组长度):")
    for difficulty, counts in difficulty_stats.items():
        if counts:
            avg_count = sum(counts) / len(counts)
            print(f"  {difficulty}: 平均 {avg_count:.2f} 个测试点 (共 {len(counts)} 题)")
    
    return validation_results

def show_sample_test_cases(file_path, num_samples=3):
    """
    显示几个样本测试用例的详细内容
    """
    print("\n" + "=" * 80)
    print(f"显示前 {num_samples} 个题目的测试用例样本:")
    print("=" * 80)
    
    df = pd.read_parquet(file_path)
    
    for idx in range(min(num_samples, len(df))):
        row = df.iloc[idx]
        slug_name = row['slug_name']
        test_cases = row['test_cases']
        
        print(f"\n题目 {idx+1}: {slug_name}")
        print("-" * 50)
        
        try:
            if isinstance(test_cases, str):
                test_cases_list = json.loads(test_cases)
                print(f"测试用例数量: {len(test_cases_list)}")
                
                for i, test_case in enumerate(test_cases_list[:2]):  # 只显示前2个测试用例
                    print(f"  测试用例 {i+1}:")
                    if isinstance(test_case, dict):
                        if "input" in test_case:
                            print(f"    input: {test_case['input']}")
                        if "expected" in test_case:
                            print(f"    expected: {test_case['expected']}")
                
                if len(test_cases_list) > 2:
                    print(f"  ... 还有 {len(test_cases_list) - 2} 个测试用例")
                    
        except (json.JSONDecodeError, TypeError) as e:
            print(f"解析错误: {e}")

def main():
    if len(sys.argv) > 1:
        file_path = sys.argv[1]
    else:
        file_path = "eval-00000-of-00001.parquet"
    
    print(f"验证文件: {file_path}")
    
    # 进行交叉验证
    results = validate_test_counts(file_path)
    
    # 显示样本测试用例
    show_sample_test_cases(file_path)
    
    # 询问是否保存结果
    choice = input("\n是否要保存验证结果到 CSV 文件? (y/n): ").lower().strip()
    if choice == 'y':
        import pandas as pd
        results_df = pd.DataFrame(results)
        output_file = "test_count_validation.csv"
        results_df.to_csv(output_file, index=False)
        print(f"验证结果已保存到: {output_file}")

if __name__ == "__main__":
    main()
